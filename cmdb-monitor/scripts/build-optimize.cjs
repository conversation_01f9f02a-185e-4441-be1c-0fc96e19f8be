#!/usr/bin/env node

/**
 * 构建优化脚本
 * 用于优化生产版本的构建过程
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 开始构建优化...\n');

// 1. 清理旧的构建文件
console.log('📁 清理旧的构建文件...');
try {
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
    console.log('✅ 清理完成');
  }
} catch (error) {
  console.error('❌ 清理失败:', error.message);
}

// 2. 运行构建
console.log('\n🔨 开始构建...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ 构建完成');
} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}

// 3. 分析构建结果
console.log('\n📊 分析构建结果...');
try {
  const distPath = path.join(__dirname, '../dist');
  const stats = analyzeBundle(distPath);
  
  console.log('构建统计:');
  console.log(`- 总文件数: ${stats.totalFiles}`);
  console.log(`- 总大小: ${formatBytes(stats.totalSize)}`);
  console.log(`- JS文件: ${stats.jsFiles} 个, ${formatBytes(stats.jsSize)}`);
  console.log(`- CSS文件: ${stats.cssFiles} 个, ${formatBytes(stats.cssSize)}`);
  console.log(`- 图片文件: ${stats.imageFiles} 个, ${formatBytes(stats.imageSize)}`);
  
  // 检查大文件
  if (stats.largeFiles.length > 0) {
    console.log('\n⚠️  发现大文件 (>500KB):');
    stats.largeFiles.forEach(file => {
      console.log(`- ${file.name}: ${formatBytes(file.size)}`);
    });
  }
  
} catch (error) {
  console.error('❌ 分析失败:', error.message);
}

// 4. 生成部署信息
console.log('\n📝 生成部署信息...');
try {
  const deployInfo = {
    buildTime: new Date().toISOString(),
    version: require('../package.json').version,
    nodeVersion: process.version,
    environment: 'production',
    features: [
      'CMDB资产管理监控',
      'IDC机房分布可视化',
      'CPU/GPU实时监控',
      '内存和网络监控',
      '告警管理系统',
      '服务器状态监控',
      '通知中心',
      '性能监控'
    ]
  };
  
  fs.writeFileSync(
    path.join(__dirname, '../dist/deploy-info.json'),
    JSON.stringify(deployInfo, null, 2)
  );
  
  console.log('✅ 部署信息已生成');
} catch (error) {
  console.error('❌ 生成部署信息失败:', error.message);
}

// 5. 生成nginx配置示例
console.log('\n🌐 生成nginx配置示例...');
try {
  const nginxConfig = `
# CMDB监控大屏 Nginx配置示例
server {
    listen 80;
    server_name your-domain.com;
    
    # 静态文件根目录
    root /path/to/cmdb-monitor/dist;
    index index.html;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态资源缓存
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理（如果需要）
    location /api/ {
        proxy_pass http://your-backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}
`;
  
  fs.writeFileSync(
    path.join(__dirname, '../dist/nginx.conf.example'),
    nginxConfig.trim()
  );
  
  console.log('✅ Nginx配置示例已生成');
} catch (error) {
  console.error('❌ 生成Nginx配置失败:', error.message);
}

console.log('\n🎉 构建优化完成！');
console.log('\n📋 后续步骤:');
console.log('1. 将 dist 目录部署到Web服务器');
console.log('2. 配置Nginx或Apache（参考 dist/nginx.conf.example）');
console.log('3. 配置HTTPS证书');
console.log('4. 设置监控和日志');

/**
 * 分析构建包
 */
function analyzeBundle(distPath) {
  const stats = {
    totalFiles: 0,
    totalSize: 0,
    jsFiles: 0,
    jsSize: 0,
    cssFiles: 0,
    cssSize: 0,
    imageFiles: 0,
    imageSize: 0,
    largeFiles: []
  };
  
  function walkDir(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        walkDir(filePath);
      } else {
        stats.totalFiles++;
        stats.totalSize += stat.size;
        
        const ext = path.extname(file).toLowerCase();
        
        if (ext === '.js') {
          stats.jsFiles++;
          stats.jsSize += stat.size;
        } else if (ext === '.css') {
          stats.cssFiles++;
          stats.cssSize += stat.size;
        } else if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico'].includes(ext)) {
          stats.imageFiles++;
          stats.imageSize += stat.size;
        }
        
        // 检查大文件 (>500KB)
        if (stat.size > 500 * 1024) {
          stats.largeFiles.push({
            name: path.relative(distPath, filePath),
            size: stat.size
          });
        }
      }
    });
  }
  
  walkDir(distPath);
  return stats;
}

/**
 * 格式化字节数
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
