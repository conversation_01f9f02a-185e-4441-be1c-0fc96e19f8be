// 监控大屏配置文件
export const DASHBOARD_CONFIG = {
  // 数据更新间隔（毫秒）
  UPDATE_INTERVALS: {
    OVERVIEW: 30000,      // 总览数据 - 30秒
    CHARTS: 10000,        // 图表数据 - 10秒
    ALERTS: 5000,         // 告警数据 - 5秒
    SERVERS: 15000,       // 服务器状态 - 15秒
  },

  // 图表配置
  CHART_CONFIG: {
    ANIMATION_DURATION: 1000,
    THEME_COLORS: {
      PRIMARY: '#1890ff',
      SUCCESS: '#52c41a',
      WARNING: '#faad14',
      ERROR: '#f5222d',
      INFO: '#13c2c2',
      PURPLE: '#722ed1',
      PINK: '#eb2f96',
    },
    GRADIENT_COLORS: [
      ['#667eea', '#764ba2'],
      ['#f093fb', '#f5576c'],
      ['#4facfe', '#00f2fe'],
      ['#43e97b', '#38f9d7'],
      ['#fa709a', '#fee140'],
    ]
  },

  // 告警配置
  ALERT_CONFIG: {
    MAX_DISPLAY_COUNT: 50,
    AUTO_REFRESH: true,
    SOUND_ENABLED: false,
    LEVELS: {
      CRITICAL: { color: '#f5222d', priority: 1 },
      WARNING: { color: '#faad14', priority: 2 },
      INFO: { color: '#1890ff', priority: 3 },
    }
  },

  // 服务器状态配置
  SERVER_CONFIG: {
    PAGE_SIZE: 20,
    AUTO_REFRESH: true,
    STATUS_COLORS: {
      ONLINE: '#52c41a',
      OFFLINE: '#f5222d',
      MAINTENANCE: '#faad14',
      UNKNOWN: '#8c8c8c',
    }
  },

  // 性能阈值配置
  THRESHOLDS: {
    CPU: {
      WARNING: 70,
      CRITICAL: 85,
    },
    GPU: {
      WARNING: 80,
      CRITICAL: 90,
    },
    MEMORY: {
      WARNING: 75,
      CRITICAL: 90,
    },
    NETWORK: {
      WARNING: 800,  // Mbps
      CRITICAL: 1000,
    }
  },

  // 显示配置
  DISPLAY_CONFIG: {
    SHOW_ANIMATIONS: true,
    COMPACT_MODE: false,
    DARK_MODE: false,
    AUTO_SCALE: true,
  }
};

// 获取状态颜色
export const getStatusColor = (type, value) => {
  const thresholds = DASHBOARD_CONFIG.THRESHOLDS[type.toUpperCase()];
  if (!thresholds) return DASHBOARD_CONFIG.CHART_CONFIG.THEME_COLORS.PRIMARY;
  
  if (value >= thresholds.CRITICAL) {
    return DASHBOARD_CONFIG.CHART_CONFIG.THEME_COLORS.ERROR;
  } else if (value >= thresholds.WARNING) {
    return DASHBOARD_CONFIG.CHART_CONFIG.THEME_COLORS.WARNING;
  } else {
    return DASHBOARD_CONFIG.CHART_CONFIG.THEME_COLORS.SUCCESS;
  }
};

// 获取状态等级
export const getStatusLevel = (type, value) => {
  const thresholds = DASHBOARD_CONFIG.THRESHOLDS[type.toUpperCase()];
  if (!thresholds) return 'normal';
  
  if (value >= thresholds.CRITICAL) {
    return 'critical';
  } else if (value >= thresholds.WARNING) {
    return 'warning';
  } else {
    return 'normal';
  }
};

// 格式化数值
export const formatValue = (value, type = 'number', unit = '') => {
  if (typeof value !== 'number') return value;
  
  switch (type) {
    case 'percentage':
      return `${value.toFixed(1)}%`;
    case 'bytes':
      if (value >= 1024 * 1024 * 1024) {
        return `${(value / (1024 * 1024 * 1024)).toFixed(1)} GB`;
      } else if (value >= 1024 * 1024) {
        return `${(value / (1024 * 1024)).toFixed(1)} MB`;
      } else if (value >= 1024) {
        return `${(value / 1024).toFixed(1)} KB`;
      } else {
        return `${value} B`;
      }
    case 'duration':
      if (value >= 86400) {
        return `${Math.floor(value / 86400)}天`;
      } else if (value >= 3600) {
        return `${Math.floor(value / 3600)}小时`;
      } else if (value >= 60) {
        return `${Math.floor(value / 60)}分钟`;
      } else {
        return `${value}秒`;
      }
    default:
      return unit ? `${value.toFixed(1)} ${unit}` : value.toFixed(1);
  }
};
