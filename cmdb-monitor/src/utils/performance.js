// 性能监控工具
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      renderTimes: [],
      dataUpdateTimes: [],
      memoryUsage: [],
      networkRequests: []
    };
    this.isMonitoring = false;
  }

  // 开始监控
  startMonitoring() {
    this.isMonitoring = true;
    this.monitorMemoryUsage();
    this.monitorNetworkRequests();
    console.log('🚀 性能监控已启动');
  }

  // 停止监控
  stopMonitoring() {
    this.isMonitoring = false;
    console.log('⏹️ 性能监控已停止');
  }

  // 记录组件渲染时间
  recordRenderTime(componentName, startTime) {
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    this.metrics.renderTimes.push({
      component: componentName,
      time: renderTime,
      timestamp: new Date()
    });

    // 如果渲染时间超过100ms，发出警告
    if (renderTime > 100) {
      console.warn(`⚠️ ${componentName} 渲染时间过长: ${renderTime.toFixed(2)}ms`);
    }

    return renderTime;
  }

  // 记录数据更新时间
  recordDataUpdateTime(dataType, startTime) {
    const endTime = performance.now();
    const updateTime = endTime - startTime;
    
    this.metrics.dataUpdateTimes.push({
      dataType,
      time: updateTime,
      timestamp: new Date()
    });

    return updateTime;
  }

  // 监控内存使用情况
  monitorMemoryUsage() {
    if (!this.isMonitoring) return;

    if (performance.memory) {
      const memoryInfo = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
        timestamp: new Date()
      };

      this.metrics.memoryUsage.push(memoryInfo);

      // 如果内存使用超过80%，发出警告
      const usagePercent = (memoryInfo.used / memoryInfo.limit) * 100;
      if (usagePercent > 80) {
        console.warn(`⚠️ 内存使用率过高: ${usagePercent.toFixed(1)}%`);
      }
    }

    // 每30秒检查一次内存使用情况
    setTimeout(() => this.monitorMemoryUsage(), 30000);
  }

  // 监控网络请求
  monitorNetworkRequests() {
    if (!this.isMonitoring) return;

    // 监控fetch请求
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const url = args[0];
      
      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();
        const requestTime = endTime - startTime;

        this.metrics.networkRequests.push({
          url,
          method: args[1]?.method || 'GET',
          status: response.status,
          time: requestTime,
          timestamp: new Date()
        });

        // 如果请求时间超过5秒，发出警告
        if (requestTime > 5000) {
          console.warn(`⚠️ 网络请求时间过长: ${url} - ${requestTime.toFixed(2)}ms`);
        }

        return response;
      } catch (error) {
        const endTime = performance.now();
        const requestTime = endTime - startTime;

        this.metrics.networkRequests.push({
          url,
          method: args[1]?.method || 'GET',
          status: 'error',
          time: requestTime,
          error: error.message,
          timestamp: new Date()
        });

        throw error;
      }
    };
  }

  // 获取性能报告
  getPerformanceReport() {
    const report = {
      summary: this.generateSummary(),
      details: {
        renderTimes: this.metrics.renderTimes.slice(-50), // 最近50次渲染
        dataUpdateTimes: this.metrics.dataUpdateTimes.slice(-50),
        memoryUsage: this.metrics.memoryUsage.slice(-20), // 最近20次内存检查
        networkRequests: this.metrics.networkRequests.slice(-100) // 最近100次请求
      },
      recommendations: this.generateRecommendations()
    };

    return report;
  }

  // 生成性能摘要
  generateSummary() {
    const renderTimes = this.metrics.renderTimes;
    const dataUpdateTimes = this.metrics.dataUpdateTimes;
    const networkRequests = this.metrics.networkRequests;

    const avgRenderTime = renderTimes.length > 0 
      ? renderTimes.reduce((sum, item) => sum + item.time, 0) / renderTimes.length 
      : 0;

    const avgDataUpdateTime = dataUpdateTimes.length > 0
      ? dataUpdateTimes.reduce((sum, item) => sum + item.time, 0) / dataUpdateTimes.length
      : 0;

    const avgNetworkTime = networkRequests.length > 0
      ? networkRequests.reduce((sum, item) => sum + item.time, 0) / networkRequests.length
      : 0;

    const currentMemory = this.metrics.memoryUsage.length > 0
      ? this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1]
      : null;

    return {
      avgRenderTime: avgRenderTime.toFixed(2),
      avgDataUpdateTime: avgDataUpdateTime.toFixed(2),
      avgNetworkTime: avgNetworkTime.toFixed(2),
      currentMemoryUsage: currentMemory ? {
        used: (currentMemory.used / 1024 / 1024).toFixed(2) + ' MB',
        usagePercent: ((currentMemory.used / currentMemory.limit) * 100).toFixed(1) + '%'
      } : null,
      totalRenders: renderTimes.length,
      totalDataUpdates: dataUpdateTimes.length,
      totalNetworkRequests: networkRequests.length
    };
  }

  // 生成性能建议
  generateRecommendations() {
    const recommendations = [];
    const renderTimes = this.metrics.renderTimes;
    const networkRequests = this.metrics.networkRequests;

    // 检查渲染性能
    const slowRenders = renderTimes.filter(item => item.time > 100);
    if (slowRenders.length > 0) {
      recommendations.push({
        type: 'render',
        level: 'warning',
        message: `发现 ${slowRenders.length} 次渲染时间超过100ms，建议优化组件渲染逻辑`
      });
    }

    // 检查网络请求
    const slowRequests = networkRequests.filter(item => item.time > 3000);
    if (slowRequests.length > 0) {
      recommendations.push({
        type: 'network',
        level: 'warning',
        message: `发现 ${slowRequests.length} 次网络请求超过3秒，建议优化数据获取策略`
      });
    }

    // 检查内存使用
    const currentMemory = this.metrics.memoryUsage.length > 0
      ? this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1]
      : null;

    if (currentMemory) {
      const usagePercent = (currentMemory.used / currentMemory.limit) * 100;
      if (usagePercent > 70) {
        recommendations.push({
          type: 'memory',
          level: usagePercent > 85 ? 'critical' : 'warning',
          message: `内存使用率 ${usagePercent.toFixed(1)}%，建议检查内存泄漏`
        });
      }
    }

    return recommendations;
  }

  // 清空监控数据
  clearMetrics() {
    this.metrics = {
      renderTimes: [],
      dataUpdateTimes: [],
      memoryUsage: [],
      networkRequests: []
    };
    console.log('📊 性能监控数据已清空');
  }

  // 导出性能数据
  exportMetrics() {
    const data = {
      timestamp: new Date().toISOString(),
      metrics: this.metrics,
      report: this.getPerformanceReport()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);

    console.log('📁 性能报告已导出');
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// React Hook for performance monitoring
export const usePerformanceMonitor = () => {
  const recordRender = (componentName) => {
    const startTime = performance.now();
    return () => performanceMonitor.recordRenderTime(componentName, startTime);
  };

  const recordDataUpdate = (dataType) => {
    const startTime = performance.now();
    return () => performanceMonitor.recordDataUpdateTime(dataType, startTime);
  };

  return {
    recordRender,
    recordDataUpdate,
    getReport: () => performanceMonitor.getPerformanceReport(),
    exportMetrics: () => performanceMonitor.exportMetrics()
  };
};

// 在开发环境中自动启动性能监控
if (process.env.NODE_ENV === 'development') {
  performanceMonitor.startMonitoring();
}
