// 通知系统
class NotificationManager {
  constructor() {
    this.notifications = [];
    this.listeners = [];
    this.maxNotifications = 5;
    this.autoRemoveDelay = 5000; // 5秒后自动移除
  }

  // 添加监听器
  addListener(callback) {
    this.listeners.push(callback);
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  // 通知所有监听器
  notifyListeners() {
    this.listeners.forEach(callback => callback(this.notifications));
  }

  // 添加通知
  addNotification(notification) {
    const id = Date.now() + Math.random();
    const newNotification = {
      id,
      timestamp: new Date(),
      ...notification
    };

    this.notifications.unshift(newNotification);
    
    // 限制通知数量
    if (this.notifications.length > this.maxNotifications) {
      this.notifications = this.notifications.slice(0, this.maxNotifications);
    }

    this.notifyListeners();

    // 自动移除通知
    if (notification.autoRemove !== false) {
      setTimeout(() => {
        this.removeNotification(id);
      }, this.autoRemoveDelay);
    }

    return id;
  }

  // 移除通知
  removeNotification(id) {
    this.notifications = this.notifications.filter(notification => notification.id !== id);
    this.notifyListeners();
  }

  // 清空所有通知
  clearAll() {
    this.notifications = [];
    this.notifyListeners();
  }

  // 添加成功通知
  success(message, title = '成功') {
    return this.addNotification({
      type: 'success',
      title,
      message,
      icon: '✅'
    });
  }

  // 添加错误通知
  error(message, title = '错误') {
    return this.addNotification({
      type: 'error',
      title,
      message,
      icon: '❌',
      autoRemove: false // 错误通知需要手动关闭
    });
  }

  // 添加警告通知
  warning(message, title = '警告') {
    return this.addNotification({
      type: 'warning',
      title,
      message,
      icon: '⚠️'
    });
  }

  // 添加信息通知
  info(message, title = '信息') {
    return this.addNotification({
      type: 'info',
      title,
      message,
      icon: 'ℹ️'
    });
  }

  // 添加告警通知
  alert(alert) {
    const typeMap = {
      critical: 'error',
      warning: 'warning',
      info: 'info'
    };

    return this.addNotification({
      type: typeMap[alert.level] || 'info',
      title: `${alert.type} - ${alert.level.toUpperCase()}`,
      message: alert.message,
      icon: alert.level === 'critical' ? '🚨' : alert.level === 'warning' ? '⚠️' : 'ℹ️',
      autoRemove: alert.level !== 'critical',
      data: alert
    });
  }
}

// 创建全局实例
export const notificationManager = new NotificationManager();

// 导出便捷方法
export const notify = {
  success: (message, title) => notificationManager.success(message, title),
  error: (message, title) => notificationManager.error(message, title),
  warning: (message, title) => notificationManager.warning(message, title),
  info: (message, title) => notificationManager.info(message, title),
  alert: (alert) => notificationManager.alert(alert)
};

// 数据变化检测器
export class DataChangeDetector {
  constructor() {
    this.previousData = {};
    this.thresholds = {
      cpu: { warning: 70, critical: 85 },
      gpu: { warning: 80, critical: 90 },
      memory: { warning: 75, critical: 90 },
      network: { warning: 800, critical: 1000 }
    };
  }

  // 检测数据变化并生成通知
  detectChanges(newData, dataType) {
    const changes = [];
    const previousData = this.previousData[dataType];

    if (!previousData) {
      this.previousData[dataType] = newData;
      return changes;
    }

    switch (dataType) {
      case 'overview':
        changes.push(...this.detectOverviewChanges(newData, previousData));
        break;
      case 'servers':
        changes.push(...this.detectServerChanges(newData, previousData));
        break;
      case 'alerts':
        changes.push(...this.detectAlertChanges(newData, previousData));
        break;
    }

    this.previousData[dataType] = newData;
    return changes;
  }

  detectOverviewChanges(newData, previousData) {
    const changes = [];

    // 检测在线率变化
    if (newData.onlineRate !== previousData.onlineRate) {
      const diff = parseFloat(newData.onlineRate) - parseFloat(previousData.onlineRate);
      if (Math.abs(diff) > 5) { // 变化超过5%
        changes.push({
          type: diff > 0 ? 'improvement' : 'degradation',
          message: `在线率${diff > 0 ? '提升' : '下降'}了${Math.abs(diff).toFixed(1)}%`,
          level: Math.abs(diff) > 10 ? 'warning' : 'info'
        });
      }
    }

    // 检测CPU使用率变化
    if (newData.avgCpuUsage !== previousData.avgCpuUsage) {
      const newCpu = parseFloat(newData.avgCpuUsage);
      const oldCpu = parseFloat(previousData.avgCpuUsage);
      
      if (newCpu > this.thresholds.cpu.critical && oldCpu <= this.thresholds.cpu.critical) {
        changes.push({
          type: 'threshold_exceeded',
          message: `CPU平均使用率超过临界值: ${newCpu}%`,
          level: 'critical'
        });
      } else if (newCpu > this.thresholds.cpu.warning && oldCpu <= this.thresholds.cpu.warning) {
        changes.push({
          type: 'threshold_exceeded',
          message: `CPU平均使用率超过警告值: ${newCpu}%`,
          level: 'warning'
        });
      }
    }

    return changes;
  }

  detectServerChanges(newData, previousData) {
    const changes = [];
    
    if (!Array.isArray(newData) || !Array.isArray(previousData)) {
      return changes;
    }

    // 检测服务器状态变化
    newData.forEach(newServer => {
      const oldServer = previousData.find(s => s.id === newServer.id);
      if (oldServer && oldServer.status !== newServer.status) {
        changes.push({
          type: 'status_change',
          message: `服务器 ${newServer.name} 状态从 ${oldServer.status} 变更为 ${newServer.status}`,
          level: newServer.status === 'offline' ? 'critical' : 'info',
          serverId: newServer.id
        });
      }
    });

    return changes;
  }

  detectAlertChanges(newData, previousData) {
    const changes = [];
    
    if (!Array.isArray(newData) || !Array.isArray(previousData)) {
      return changes;
    }

    // 检测新增告警
    const newAlerts = newData.filter(alert => 
      !previousData.find(oldAlert => oldAlert.id === alert.id)
    );

    newAlerts.forEach(alert => {
      if (alert.status === 'active') {
        changes.push({
          type: 'new_alert',
          message: `新告警: ${alert.message}`,
          level: alert.level,
          alertId: alert.id
        });
      }
    });

    return changes;
  }
}

// 创建全局数据变化检测器实例
export const dataChangeDetector = new DataChangeDetector();
