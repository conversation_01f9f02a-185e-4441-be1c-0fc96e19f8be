import { useState, useEffect, useCallback } from 'react';
import { getMockData } from '../utils/mockData';

// 实时数据管理Hook
export const useRealTimeData = (updateInterval = 30000) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(null);

  // 更新数据的方法
  const updateData = useCallback(() => {
    setLoading(true);
    try {
      const newData = getMockData();
      setData(newData);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to update data:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化数据和设置定时更新
  useEffect(() => {
    // 立即加载一次数据
    updateData();

    // 设置定时更新
    const interval = setInterval(updateData, updateInterval);

    // 清理定时器
    return () => clearInterval(interval);
  }, [updateData, updateInterval]);

  // 手动刷新数据
  const refreshData = useCallback(() => {
    updateData();
  }, [updateData]);

  return {
    data,
    loading,
    lastUpdate,
    refreshData
  };
};

// 专门用于图表数据的Hook
export const useChartData = (dataKey, updateInterval = 10000) => {
  const [chartData, setChartData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const updateChartData = () => {
      setLoading(true);
      try {
        const allData = getMockData();
        if (allData[dataKey]) {
          setChartData(allData[dataKey]);
        }
      } catch (error) {
        console.error(`Failed to update ${dataKey} data:`, error);
      } finally {
        setLoading(false);
      }
    };

    // 立即更新一次
    updateChartData();

    // 设置定时更新
    const interval = setInterval(updateChartData, updateInterval);

    return () => clearInterval(interval);
  }, [dataKey, updateInterval]);

  return { chartData, loading };
};
