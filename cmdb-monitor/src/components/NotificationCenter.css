.notification-center {
  position: relative;
  z-index: 1000;
}

/* 通知按钮 */
.notification-toggle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
}

.notification-toggle:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-toggle.has-unread {
  color: #1890ff;
  animation: pulse 2s infinite;
}

.notification-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: #f5222d;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: bold;
  min-width: 16px;
  text-align: center;
}

/* 通知面板 */
.notification-panel {
  position: absolute;
  top: 50px;
  right: 0;
  width: 350px;
  max-height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  overflow: hidden;
  animation: slideInDown 0.3s ease-out;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.notification-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.notification-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.clear-all-btn {
  padding: 4px 8px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-all-btn:hover {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #999;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f5f5f5;
  color: #333;
}

.notification-content {
  max-height: 400px;
  overflow-y: auto;
}

.notification-content::-webkit-scrollbar {
  width: 6px;
}

.notification-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.notification-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.no-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #999;
}

.no-notifications svg {
  margin-bottom: 12px;
  opacity: 0.5;
}

.notification-list {
  padding: 8px 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 20px;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.3s ease;
  position: relative;
}

.notification-item:hover {
  background: #f9f9f9;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.success {
  border-left: 4px solid #52c41a;
}

.notification-item.error {
  border-left: 4px solid #f5222d;
}

.notification-item.warning {
  border-left: 4px solid #faad14;
}

.notification-item.info {
  border-left: 4px solid #1890ff;
}

.notification-icon {
  margin-right: 12px;
  font-size: 16px;
  margin-top: 2px;
}

.notification-body {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  margin-bottom: 4px;
}

.notification-message {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.notification-time {
  color: #999;
  font-size: 11px;
}

.notification-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #ccc;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.notification-close:hover {
  background: #f5f5f5;
  color: #999;
}

/* 浮动通知 */
.floating-notifications {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 12px;
  pointer-events: none;
}

.floating-notification {
  display: flex;
  align-items: center;
  min-width: 300px;
  max-width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  padding: 12px 16px;
  pointer-events: auto;
  animation: slideInRight 0.3s ease-out;
  position: relative;
}

.floating-notification.success {
  border-left-color: #52c41a;
}

.floating-notification.error {
  border-left-color: #f5222d;
}

.floating-notification.warning {
  border-left-color: #faad14;
}

.floating-notification.info {
  border-left-color: #1890ff;
}

.floating-notification-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.floating-notification-icon {
  margin-right: 12px;
  font-size: 16px;
}

.floating-notification-text {
  flex: 1;
}

.floating-notification-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  margin-bottom: 2px;
}

.floating-notification-message {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

.floating-notification-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #ccc;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.floating-notification-close:hover {
  background: #f5f5f5;
  color: #999;
}

/* 动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-panel {
    width: 300px;
    right: -20px;
  }
  
  .floating-notifications {
    right: 10px;
    top: 10px;
  }
  
  .floating-notification {
    min-width: 280px;
    max-width: 320px;
  }
}
