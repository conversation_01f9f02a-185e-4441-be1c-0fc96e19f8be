import React from 'react';
import './ProfessionalCard.css';

const ProfessionalCard = ({ 
  icon: IconComponent, 
  title, 
  value, 
  unit, 
  subtitle, 
  trend, 
  status = 'normal',
  isCore = false 
}) => {
  const getStatusColor = () => {
    switch (status) {
      case 'critical': return '#dc2626';
      case 'warning': return '#d97706';
      case 'success': return '#059669';
      default: return '#374151';
    }
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    if (trend > 0) return '↗';
    if (trend < 0) return '↘';
    return '→';
  };

  const getTrendColor = () => {
    if (!trend) return '#6b7280';
    if (trend > 0) return '#059669';
    if (trend < 0) return '#dc2626';
    return '#6b7280';
  };

  return (
    <div className={`professional-card ${isCore ? 'core-metric' : ''} ${status}`}>
      {isCore && <div className="core-indicator">核心指标</div>}
      
      <div className="card-header">
        <div className="icon-container" style={{ color: getStatusColor() }}>
          {IconComponent && <IconComponent size={24} strokeWidth={1.5} />}
        </div>
        <div className="card-meta">
          <h3 className="card-title">{title}</h3>
          {trend !== undefined && (
            <div className="trend-indicator" style={{ color: getTrendColor() }}>
              <span className="trend-icon">{getTrendIcon()}</span>
              <span className="trend-value">{Math.abs(trend)}%</span>
            </div>
          )}
        </div>
      </div>

      <div className="card-content">
        <div className="primary-value">
          <span className="value" style={{ color: getStatusColor() }}>
            {value}
          </span>
          {unit && <span className="unit">{unit}</span>}
        </div>
        
        {subtitle && (
          <div className="subtitle">{subtitle}</div>
        )}
      </div>

      <div className="card-footer">
        <div className={`status-indicator ${status}`}>
          <div className="status-dot"></div>
          <span className="status-text">
            {status === 'critical' ? '严重' : 
             status === 'warning' ? '警告' : 
             status === 'success' ? '正常' : '正常'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ProfessionalCard;
