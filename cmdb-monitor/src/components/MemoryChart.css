.memory-chart {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  position: relative;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #666;
  font-size: 16px;
}

.memory-stats {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.stat-row:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stat-value {
  font-size: 14px;
  font-weight: bold;
  color: #1890ff;
}

.stat-value.normal {
  color: #52c41a;
}

.stat-value.warning {
  color: #faad14;
}

.stat-value.danger {
  color: #f5222d;
}

.usage-bar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  margin-top: 12px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  border-radius: 4px;
  transition: all 0.3s ease;
  background: #52c41a;
}

.usage-fill.warning {
  background: #faad14;
}

.usage-fill.danger {
  background: #f5222d;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .memory-chart {
    padding: 16px;
  }
  
  .chart-container {
    min-height: 280px;
  }
  
  .stat-row {
    padding: 6px 0;
  }
  
  .stat-label,
  .stat-value {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .memory-chart {
    padding: 12px;
  }
  
  .chart-container {
    min-height: 250px;
  }
  
  .stat-row {
    padding: 4px 0;
  }
  
  .stat-label,
  .stat-value {
    font-size: 12px;
  }
  
  .usage-bar {
    height: 6px;
    margin-top: 8px;
  }
}

/* 动画效果 */
.memory-chart {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-row {
  animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 为统计行添加延迟动画 */
.stat-row:nth-child(1) { animation-delay: 0.1s; }
.stat-row:nth-child(2) { animation-delay: 0.2s; }
.stat-row:nth-child(3) { animation-delay: 0.3s; }
.stat-row:nth-child(4) { animation-delay: 0.4s; }
.stat-row:nth-child(5) { animation-delay: 0.5s; }

.usage-fill {
  animation: fillBar 1s ease-out 0.5s both;
}

@keyframes fillBar {
  from {
    width: 0;
  }
  to {
    width: var(--target-width, 0);
  }
}

/* 危险状态的脉冲动画 */
.stat-value.danger {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}
