import React, { useState, useMemo } from 'react';
import { Server, Filter, Search, RefreshCw } from 'lucide-react';
import './ServerStatus.css';

const ServerStatus = ({ data, title = '服务器状态', onRefresh }) => {
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // 过滤和搜索数据
  const filteredData = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];
    
    return data.filter(server => {
      const statusMatch = filterStatus === 'all' || server.status === filterStatus;
      const typeMatch = filterType === 'all' || server.type === filterType;
      const searchMatch = searchTerm === '' || 
        server.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        server.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        server.room.toLowerCase().includes(searchTerm.toLowerCase());
      
      return statusMatch && typeMatch && searchMatch;
    });
  }, [data, filterStatus, filterType, searchTerm]);

  // 获取状态文本和样式
  const getStatusInfo = (status) => {
    switch (status) {
      case 'online':
        return { text: '在线', className: 'online' };
      case 'offline':
        return { text: '离线', className: 'offline' };
      case 'maintenance':
        return { text: '维护', className: 'maintenance' };
      default:
        return { text: '未知', className: 'unknown' };
    }
  };

  // 获取唯一的服务器类型
  const serverTypes = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];
    return [...new Set(data.map(server => server.type))];
  }, [data]);

  // 统计信息
  const stats = useMemo(() => {
    if (!data || !Array.isArray(data)) return null;
    
    const total = data.length;
    const online = data.filter(server => server.status === 'online').length;
    const offline = data.filter(server => server.status === 'offline').length;
    const maintenance = data.filter(server => server.status === 'maintenance').length;
    
    return { total, online, offline, maintenance };
  }, [data]);

  if (!data || !Array.isArray(data)) {
    return (
      <div className="server-status">
        <div className="loading">加载中...</div>
      </div>
    );
  }

  return (
    <div className="server-status">
      <div className="server-header">
        <div className="header-left">
          <h3 className="server-title">
            <Server size={20} />
            {title}
          </h3>
          
          {stats && (
            <div className="server-stats">
              <span className="stat-item total">总计: {stats.total}</span>
              <span className="stat-item online">在线: {stats.online}</span>
              <span className="stat-item offline">离线: {stats.offline}</span>
              <span className="stat-item maintenance">维护: {stats.maintenance}</span>
            </div>
          )}
        </div>
        
        <button className="refresh-btn" onClick={onRefresh}>
          <RefreshCw size={16} />
          刷新
        </button>
      </div>

      <div className="server-controls">
        <div className="search-box">
          <Search size={16} />
          <input
            type="text"
            placeholder="搜索服务器..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="filter-group">
          <Filter size={16} />
          <select 
            value={filterStatus} 
            onChange={(e) => setFilterStatus(e.target.value)}
            className="filter-select"
          >
            <option value="all">所有状态</option>
            <option value="online">在线</option>
            <option value="offline">离线</option>
            <option value="maintenance">维护</option>
          </select>
          
          <select 
            value={filterType} 
            onChange={(e) => setFilterType(e.target.value)}
            className="filter-select"
          >
            <option value="all">所有类型</option>
            {serverTypes.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>
      </div>

      <div className="server-table-container">
        <table className="server-table">
          <thead>
            <tr>
              <th>服务器ID</th>
              <th>名称</th>
              <th>类型</th>
              <th>机房</th>
              <th>状态</th>
              <th>CPU</th>
              <th>GPU</th>
              <th>内存</th>
              <th>运行时间</th>
              <th>最后更新</th>
            </tr>
          </thead>
          <tbody>
            {filteredData.map((server, index) => {
              const statusInfo = getStatusInfo(server.status);
              return (
                <tr key={server.id || index} className={`server-row ${statusInfo.className}`}>
                  <td className="server-id">{server.id}</td>
                  <td className="server-name">{server.name}</td>
                  <td className="server-type">{server.type}</td>
                  <td className="server-room">{server.room}</td>
                  <td className="server-status">
                    <span className={`status-badge ${statusInfo.className}`}>
                      {statusInfo.text}
                    </span>
                  </td>
                  <td className="server-cpu">{server.cpuUsage}%</td>
                  <td className="server-gpu">{server.gpuUsage}%</td>
                  <td className="server-memory">{server.memoryUsage}%</td>
                  <td className="server-uptime">{server.uptime}</td>
                  <td className="server-update">{server.lastUpdate}</td>
                </tr>
              );
            })}
          </tbody>
        </table>
        
        {filteredData.length === 0 && (
          <div className="no-servers">
            <Server size={48} />
            <p>没有找到匹配的服务器</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ServerStatus;
