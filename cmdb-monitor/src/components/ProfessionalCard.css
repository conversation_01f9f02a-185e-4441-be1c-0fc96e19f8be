.professional-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 24px;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  min-height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.professional-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #cbd5e1;
}

.professional-card.core-metric {
  border: 2px solid #3b82f6;
  background: linear-gradient(145deg, #ffffff 0%, #eff6ff 100%);
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1), 0 2px 4px -1px rgba(59, 130, 246, 0.06);
}

.professional-card.core-metric:hover {
  box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
}

.core-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16px;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.professional-card:hover .icon-container {
  background: rgba(241, 245, 249, 1);
  border-color: #cbd5e1;
}

.card-meta {
  flex: 1;
  margin-left: 16px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 4px 0;
  line-height: 1.4;
  letter-spacing: -0.025em;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.trend-icon {
  font-size: 14px;
}

.trend-value {
  font-size: 11px;
}

.card-content {
  margin-bottom: 16px;
}

.primary-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.value {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  letter-spacing: -0.025em;
}

.unit {
  font-size: 16px;
  font-weight: 500;
  color: #6b7280;
  margin-left: 4px;
}

.subtitle {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
  line-height: 1.4;
}

.card-footer {
  margin-top: auto;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #6b7280;
  transition: all 0.3s ease;
}

.status-indicator.critical .status-dot {
  background: #dc2626;
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
}

.status-indicator.warning .status-dot {
  background: #d97706;
  box-shadow: 0 0 0 2px rgba(217, 119, 6, 0.2);
}

.status-indicator.success .status-dot {
  background: #059669;
  box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.2);
}

.status-indicator.normal .status-dot {
  background: #374151;
  box-shadow: 0 0 0 2px rgba(55, 65, 81, 0.2);
}

.status-text {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .professional-card {
    padding: 20px;
    min-height: 140px;
  }
  
  .card-header {
    margin-bottom: 12px;
  }
  
  .icon-container {
    width: 40px;
    height: 40px;
  }
  
  .card-meta {
    margin-left: 12px;
  }
  
  .card-title {
    font-size: 13px;
  }
  
  .value {
    font-size: 28px;
  }
  
  .unit {
    font-size: 14px;
  }
}

/* 动画效果 */
.professional-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为卡片添加延迟动画 */
.professional-card:nth-child(1) { animation-delay: 0.1s; }
.professional-card:nth-child(2) { animation-delay: 0.2s; }
.professional-card:nth-child(3) { animation-delay: 0.3s; }
.professional-card:nth-child(4) { animation-delay: 0.4s; }
.professional-card:nth-child(5) { animation-delay: 0.5s; }
.professional-card:nth-child(6) { animation-delay: 0.6s; }
.professional-card:nth-child(7) { animation-delay: 0.7s; }
.professional-card:nth-child(8) { animation-delay: 0.8s; }
