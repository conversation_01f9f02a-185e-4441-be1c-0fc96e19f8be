import React, { useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import './MemoryChart.css';

const MemoryChart = ({ data, title = '内存使用分布' }) => {
  const chartOption = useMemo(() => {
    if (!data || !Array.isArray(data)) return {};

    const chartData = data.map(item => ({
      name: item.name,
      value: parseFloat(item.value),
      percentage: parseFloat(item.percentage)
    }));

    const colors = ['#f5222d', '#faad14', '#52c41a'];

    return {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#333'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${params.name}</div>
              <div>大小: ${params.value} GB</div>
              <div>占比: ${params.data.percentage}%</div>
            </div>
          `;
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        }
      },
      legend: {
        orient: 'horizontal',
        bottom: 20,
        textStyle: {
          color: '#666'
        },
        formatter: function(name) {
          const item = chartData.find(d => d.name === name);
          return item ? `${name} (${item.percentage}%)` : name;
        }
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '55%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'outside',
            formatter: function(params) {
              return `${params.name}\n${params.value}GB\n${params.data.percentage}%`;
            },
            fontSize: 12,
            color: '#333'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold'
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: true,
            lineStyle: {
              color: '#999'
            }
          },
          data: chartData.map((item, index) => ({
            ...item,
            itemStyle: {
              color: colors[index % colors.length]
            }
          }))
        }
      ]
    };
  }, [data, title]);

  // 计算总内存和使用率
  const memoryStats = useMemo(() => {
    if (!data || !Array.isArray(data)) return null;
    
    const totalMemory = data.reduce((sum, item) => sum + parseFloat(item.value), 0);
    const usedMemory = data.find(item => item.name === '已使用');
    const usedPercentage = usedMemory ? parseFloat(usedMemory.percentage) : 0;
    
    return {
      total: totalMemory.toFixed(0),
      used: usedMemory ? parseFloat(usedMemory.value).toFixed(0) : '0',
      usedPercentage: usedPercentage.toFixed(1),
      status: usedPercentage > 85 ? 'danger' : usedPercentage > 70 ? 'warning' : 'normal'
    };
  }, [data]);

  if (!data || !Array.isArray(data)) {
    return (
      <div className="memory-chart">
        <div className="loading">加载中...</div>
      </div>
    );
  }

  return (
    <div className="memory-chart">
      <div className="chart-container">
        <ReactECharts 
          option={chartOption} 
          style={{ height: '300px', width: '100%' }}
          opts={{ renderer: 'canvas' }}
        />
      </div>
      
      {memoryStats && (
        <div className="memory-stats">
          <div className="stat-row">
            <span className="stat-label">总内存</span>
            <span className="stat-value">{memoryStats.total} GB</span>
          </div>
          <div className="stat-row">
            <span className="stat-label">已使用</span>
            <span className={`stat-value ${memoryStats.status}`}>
              {memoryStats.used} GB ({memoryStats.usedPercentage}%)
            </span>
          </div>
          <div className="usage-bar">
            <div 
              className={`usage-fill ${memoryStats.status}`}
              style={{ width: `${memoryStats.usedPercentage}%` }}
            ></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MemoryChart;
