import React, { useMemo, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import ProfessionalChartContainer from './ProfessionalChartContainer';
import { notificationManager } from '../utils/notification';
import './CpuChart.css';

const CpuChart = ({ data, title = 'CPU使用率趋势', onRefresh }) => {
  const chartOption = useMemo(() => {
    if (!data || !Array.isArray(data)) return {};

    const times = data.map(item => item.time);
    const server1Data = data.map(item => parseFloat(item.server1));
    const server2Data = data.map(item => parseFloat(item.server2));
    const server3Data = data.map(item => parseFloat(item.server3));
    const averageData = data.map(item => parseFloat(item.average));

    return {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#333'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: function(params) {
          let result = `<div style="padding: 8px;"><strong>${params[0].axisValue}</strong><br/>`;
          params.forEach(param => {
            result += `<div style="margin: 4px 0;">
              <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
              ${param.seriesName}: ${param.value}%
            </div>`;
          });
          result += '</div>';
          return result;
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        }
      },
      legend: {
        data: ['服务器1', '服务器2', '服务器3', '平均值'],
        top: 50,
        textStyle: {
          color: '#666'
        }
      },
      grid: {
        left: '8%',
        right: '8%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: times,
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
          formatter: '{value}%'
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '服务器1',
          type: 'line',
          data: server1Data,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 2
          },
          itemStyle: {
            color: '#1890ff'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.05)' }
              ]
            }
          }
        },
        {
          name: '服务器2',
          type: 'line',
          data: server2Data,
          smooth: true,
          lineStyle: {
            color: '#52c41a',
            width: 2
          },
          itemStyle: {
            color: '#52c41a'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(82, 196, 26, 0.3)' },
                { offset: 1, color: 'rgba(82, 196, 26, 0.05)' }
              ]
            }
          }
        },
        {
          name: '服务器3',
          type: 'line',
          data: server3Data,
          smooth: true,
          lineStyle: {
            color: '#fa8c16',
            width: 2
          },
          itemStyle: {
            color: '#fa8c16'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(250, 140, 22, 0.3)' },
                { offset: 1, color: 'rgba(250, 140, 22, 0.05)' }
              ]
            }
          }
        },
        {
          name: '平均值',
          type: 'line',
          data: averageData,
          smooth: true,
          lineStyle: {
            color: '#722ed1',
            width: 3,
            type: 'dashed'
          },
          itemStyle: {
            color: '#722ed1',
            borderWidth: 2
          },
          symbol: 'circle',
          symbolSize: 6
        }
      ]
    };
  }, [data, title]);

  // 计算统计信息
  const stats = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return null;
    
    const latest = data[data.length - 1];
    const avgCpu = parseFloat(latest.average);
    const maxCpu = Math.max(
      parseFloat(latest.server1),
      parseFloat(latest.server2),
      parseFloat(latest.server3)
    );
    const minCpu = Math.min(
      parseFloat(latest.server1),
      parseFloat(latest.server2),
      parseFloat(latest.server3)
    );
    
    return {
      current: avgCpu.toFixed(1),
      max: maxCpu.toFixed(1),
      min: minCpu.toFixed(1),
      status: avgCpu > 80 ? 'danger' : avgCpu > 60 ? 'warning' : 'normal'
    };
  }, [data]);

  // 处理图表控制事件
  const handleRefresh = () => {
    onRefresh && onRefresh();
    notificationManager.info('CPU数据已刷新');
  };

  const handleExport = (format) => {
    notificationManager.info(`正在导出${format.toUpperCase()}格式...`);
  };

  const handleFullscreen = () => {
    notificationManager.info('全屏功能开发中...');
  };

  if (!data || !Array.isArray(data)) {
    return (
      <ProfessionalChartContainer
        title={title}
        subtitle="实时监控CPU使用率变化趋势"
        status="normal"
      >
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '300px' }}>
          <div>加载中...</div>
        </div>
      </ProfessionalChartContainer>
    );
  }

  // 准备指标数据
  const metrics = stats ? [
    {
      id: 'current',
      label: '当前平均',
      value: `${stats.current}%`,
      trend: 'neutral'
    },
    {
      id: 'max',
      label: '最高值',
      value: `${stats.max}%`,
      trend: 'up'
    },
    {
      id: 'min',
      label: '最低值',
      value: `${stats.min}%`,
      trend: 'down'
    }
  ] : [];

  // 确定状态
  const chartStatus = stats ?
    (stats.status === 'danger' ? 'error' :
     stats.status === 'warning' ? 'warning' : 'normal') : 'normal';

  return (
    <ProfessionalChartContainer
      title={title}
      subtitle="实时监控CPU使用率变化趋势"
      metrics={metrics}
      status={chartStatus}
      onRefresh={handleRefresh}
      onExport={handleExport}
      onFullscreen={handleFullscreen}
      lastUpdated={new Date()}
    >
      <ReactECharts
        option={chartOption}
        style={{ height: '300px', width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
    </ProfessionalChartContainer>
  );
};

export default CpuChart;
