.alert-list {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.alert-header {
  margin-bottom: 16px;
}

.alert-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0 0 12px 0;
}

.alert-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.stat-item.critical {
  background: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}

.stat-item.warning {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.stat-item.info {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.stat-item.active {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.alert-filters {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-select {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.alert-content {
  flex: 1;
  overflow: hidden;
}

.alert-items {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.alert-items::-webkit-scrollbar {
  width: 6px;
}

.alert-items::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.alert-items::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.alert-items::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  border-left: 4px solid;
  background: #fafafa;
  transition: all 0.3s ease;
}

.alert-item:hover {
  background: #f0f0f0;
  transform: translateX(4px);
}

.alert-item.critical {
  border-left-color: #f5222d;
  background: rgba(245, 34, 45, 0.05);
}

.alert-item.warning {
  border-left-color: #faad14;
  background: rgba(250, 173, 20, 0.05);
}

.alert-item.info {
  border-left-color: #1890ff;
  background: rgba(24, 144, 255, 0.05);
}

.alert-item.resolved {
  opacity: 0.7;
}

.alert-icon {
  margin-right: 12px;
  margin-top: 2px;
  color: inherit;
}

.alert-item.critical .alert-icon {
  color: #f5222d;
}

.alert-item.warning .alert-icon {
  color: #faad14;
}

.alert-item.info .alert-icon {
  color: #1890ff;
}

.alert-info {
  flex: 1;
}

.alert-main {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.alert-type {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.alert-level {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.alert-level.critical {
  background: #f5222d;
  color: white;
}

.alert-level.warning {
  background: #faad14;
  color: white;
}

.alert-level.info {
  background: #1890ff;
  color: white;
}

.alert-message {
  color: #666;
  font-size: 13px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.alert-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #999;
}

.alert-server {
  font-family: monospace;
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
}

.alert-time {
  color: #666;
}

.alert-status {
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.alert-status.active {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.alert-status.resolved {
  background: rgba(140, 140, 140, 0.1);
  color: #8c8c8c;
}

.no-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.no-alerts svg {
  margin-bottom: 12px;
  color: #52c41a;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alert-list {
    padding: 16px;
  }
  
  .alert-stats {
    gap: 8px;
  }
  
  .stat-item {
    font-size: 11px;
    padding: 3px 6px;
  }
  
  .filter-group {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .alert-item {
    padding: 10px;
  }
  
  .alert-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 动画效果 */
.alert-item {
  animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 为告警项添加延迟动画 */
.alert-item:nth-child(1) { animation-delay: 0.1s; }
.alert-item:nth-child(2) { animation-delay: 0.2s; }
.alert-item:nth-child(3) { animation-delay: 0.3s; }
.alert-item:nth-child(4) { animation-delay: 0.4s; }
.alert-item:nth-child(5) { animation-delay: 0.5s; }
