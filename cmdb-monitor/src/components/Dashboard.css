.dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: #f1f5f9;
}

.dashboard-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  text-align: center;
}

.loading-spinner p {
  margin-top: 16px;
  font-size: 18px;
  font-weight: 500;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 头部样式 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(71, 85, 105, 0.3);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.3);
}

.header-left {
  flex: 1;
}

.dashboard-title {
  font-size: 32px;
  font-weight: 700;
  color: #f1f5f9;
  margin: 0 0 6px 0;
  letter-spacing: -0.025em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-subtitle {
  font-size: 16px;
  color: #94a3b8;
  margin: 0;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.last-update {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #cbd5e1;
  padding: 10px 16px;
  background: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(71, 85, 105, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.4);
  letter-spacing: 0.025em;
}

.refresh-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.5);
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}

.refresh-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* 主要内容区域 */
.dashboard-main {
  flex: 1;
  padding: 20px 30px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.overview-section {
  width: 100%;
}

/* IDC核心展示区域 */
.idc-core-section {
  width: 100%;
  margin-bottom: 30px;
}

.idc-core-chart {
  background: rgba(15, 23, 42, 0.9);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(59, 130, 246, 0.4);
  overflow: hidden;
  transition: all 0.3s ease;
  min-height: 500px;
}

.idc-core-chart:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.6);
}

/* 图表行布局 */
.charts-row-1 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  height: 400px;
}

.charts-row-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  height: 400px;
}

.charts-row-1 .cpu-chart {
  grid-column: 1;
}

.charts-row-1 .gpu-chart {
  grid-column: 2;
}

.charts-row-1 .memory-chart {
  grid-column: 3;
}

.charts-row-2 .network-chart {
  grid-column: 1;
}

.charts-row-2 .alert-chart {
  grid-column: 2;
}

.chart-item {
  background: rgba(15, 23, 42, 0.8);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(71, 85, 105, 0.3);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(71, 85, 105, 0.5);
}

.server-section {
  background: rgba(15, 23, 42, 0.8);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(71, 85, 105, 0.3);
  overflow: hidden;
  min-height: 500px;
}

/* 页脚样式 */
.dashboard-footer {
  padding: 24px 32px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(71, 85, 105, 0.3);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #94a3b8;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .charts-row-1,
  .charts-row-2 {
    height: 400px;
  }
  
  .dashboard-title {
    font-size: 24px;
  }
}

@media (max-width: 1200px) {
  .dashboard-main {
    padding: 16px 20px;
    gap: 20px;
  }
  
  .dashboard-header {
    padding: 16px 20px;
  }
  
  .charts-row-1,
  .charts-row-2 {
    grid-template-columns: 1fr 1fr;
    height: auto;
    gap: 16px;
  }
  
  .charts-row-1 .idc-chart {
    grid-column: 1 / -1;
  }
  
  .charts-row-1 .cpu-chart {
    grid-column: 1;
  }
  
  .charts-row-1 .gpu-chart {
    grid-column: 2;
  }
  
  .charts-row-2 .memory-chart {
    grid-column: 1;
  }
  
  .charts-row-2 .network-chart {
    grid-column: 2;
  }
  
  .charts-row-2 .alert-chart {
    grid-column: 1 / -1;
  }
  
  .chart-item {
    min-height: 350px;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding: 16px;
  }
  
  .header-right {
    justify-content: space-between;
  }
  
  .dashboard-main {
    padding: 12px 16px;
    gap: 16px;
  }
  
  .charts-row-1,
  .charts-row-2 {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .charts-row-1 .idc-chart,
  .charts-row-1 .cpu-chart,
  .charts-row-1 .gpu-chart,
  .charts-row-2 .memory-chart,
  .charts-row-2 .network-chart,
  .charts-row-2 .alert-chart {
    grid-column: 1;
  }
  
  .chart-item {
    min-height: 300px;
  }
  
  .dashboard-title {
    font-size: 20px;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .dashboard-footer {
    padding: 16px;
  }
}

/* 动画效果 */
.dashboard {
  animation: fadeIn 1s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.chart-item {
  animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为图表项添加延迟动画 */
.charts-row-1 .chart-item:nth-child(1) { animation-delay: 0.2s; }
.charts-row-1 .chart-item:nth-child(2) { animation-delay: 0.4s; }
.charts-row-1 .chart-item:nth-child(3) { animation-delay: 0.6s; }
.charts-row-2 .chart-item:nth-child(1) { animation-delay: 0.8s; }
.charts-row-2 .chart-item:nth-child(2) { animation-delay: 1.0s; }
.charts-row-2 .chart-item:nth-child(3) { animation-delay: 1.2s; }
.server-section { animation-delay: 1.4s; }
