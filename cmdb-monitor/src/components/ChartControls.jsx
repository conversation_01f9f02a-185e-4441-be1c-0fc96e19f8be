import React, { useState } from 'react';
import { Play, Pause, RotateCcw, Download, Maximize2, Settings } from 'lucide-react';
import './ChartControls.css';

const ChartControls = ({ 
  onRefresh, 
  onToggleAutoRefresh, 
  onExport, 
  onFullscreen,
  onSettingsChange,
  isAutoRefresh = true,
  refreshInterval = 30,
  title = "图表控制"
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState({
    refreshInterval: refreshInterval,
    showAnimation: true,
    showDataLabels: true,
    chartTheme: 'default'
  });

  const handleSettingChange = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    onSettingsChange && onSettingsChange(newSettings);
  };

  const handleExport = (format) => {
    onExport && onExport(format);
  };

  return (
    <div className="chart-controls">
      <div className="controls-group">
        {/* 自动刷新控制 */}
        <button
          className={`control-btn ${isAutoRefresh ? 'active' : ''}`}
          onClick={onToggleAutoRefresh}
          title={isAutoRefresh ? '暂停自动刷新' : '开启自动刷新'}
        >
          {isAutoRefresh ? <Pause size={16} /> : <Play size={16} />}
        </button>

        {/* 手动刷新 */}
        <button
          className="control-btn"
          onClick={onRefresh}
          title="手动刷新"
        >
          <RotateCcw size={16} />
        </button>

        {/* 全屏显示 */}
        <button
          className="control-btn"
          onClick={onFullscreen}
          title="全屏显示"
        >
          <Maximize2 size={16} />
        </button>

        {/* 导出数据 */}
        <div className="dropdown-container">
          <button
            className="control-btn"
            title="导出数据"
          >
            <Download size={16} />
          </button>
          <div className="dropdown-menu">
            <button onClick={() => handleExport('png')}>导出为PNG</button>
            <button onClick={() => handleExport('jpg')}>导出为JPG</button>
            <button onClick={() => handleExport('svg')}>导出为SVG</button>
            <button onClick={() => handleExport('csv')}>导出为CSV</button>
          </div>
        </div>

        {/* 设置 */}
        <button
          className={`control-btn ${showSettings ? 'active' : ''}`}
          onClick={() => setShowSettings(!showSettings)}
          title="图表设置"
        >
          <Settings size={16} />
        </button>
      </div>

      {/* 设置面板 */}
      {showSettings && (
        <div className="settings-panel">
          <div className="settings-header">
            <h4>图表设置</h4>
            <button 
              className="close-settings"
              onClick={() => setShowSettings(false)}
            >
              ×
            </button>
          </div>
          
          <div className="settings-content">
            {/* 刷新间隔 */}
            <div className="setting-item">
              <label>刷新间隔 (秒)</label>
              <select
                value={settings.refreshInterval}
                onChange={(e) => handleSettingChange('refreshInterval', parseInt(e.target.value))}
              >
                <option value={5}>5秒</option>
                <option value={10}>10秒</option>
                <option value={30}>30秒</option>
                <option value={60}>1分钟</option>
                <option value={300}>5分钟</option>
              </select>
            </div>

            {/* 动画效果 */}
            <div className="setting-item">
              <label>
                <input
                  type="checkbox"
                  checked={settings.showAnimation}
                  onChange={(e) => handleSettingChange('showAnimation', e.target.checked)}
                />
                显示动画效果
              </label>
            </div>

            {/* 数据标签 */}
            <div className="setting-item">
              <label>
                <input
                  type="checkbox"
                  checked={settings.showDataLabels}
                  onChange={(e) => handleSettingChange('showDataLabels', e.target.checked)}
                />
                显示数据标签
              </label>
            </div>

            {/* 图表主题 */}
            <div className="setting-item">
              <label>图表主题</label>
              <select
                value={settings.chartTheme}
                onChange={(e) => handleSettingChange('chartTheme', e.target.value)}
              >
                <option value="default">默认</option>
                <option value="dark">深色</option>
                <option value="light">浅色</option>
                <option value="colorful">彩色</option>
              </select>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// 时间范围选择器组件
export const TimeRangeSelector = ({ 
  value = '24h', 
  onChange, 
  options = [
    { value: '1h', label: '1小时' },
    { value: '6h', label: '6小时' },
    { value: '24h', label: '24小时' },
    { value: '7d', label: '7天' },
    { value: '30d', label: '30天' }
  ]
}) => {
  return (
    <div className="time-range-selector">
      <label>时间范围:</label>
      <select value={value} onChange={(e) => onChange(e.target.value)}>
        {options.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};

// 数据筛选器组件
export const DataFilter = ({ 
  filters = [], 
  activeFilters = [], 
  onChange 
}) => {
  const handleFilterToggle = (filterId) => {
    const newActiveFilters = activeFilters.includes(filterId)
      ? activeFilters.filter(id => id !== filterId)
      : [...activeFilters, filterId];
    onChange(newActiveFilters);
  };

  return (
    <div className="data-filter">
      <label>数据筛选:</label>
      <div className="filter-buttons">
        {filters.map(filter => (
          <button
            key={filter.id}
            className={`filter-btn ${activeFilters.includes(filter.id) ? 'active' : ''}`}
            onClick={() => handleFilterToggle(filter.id)}
            style={{ color: filter.color }}
          >
            {filter.label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ChartControls;
