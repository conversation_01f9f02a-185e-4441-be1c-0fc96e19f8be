import React from 'react';
import { Server, Activity, Cpu, HardDrive, Wifi, AlertTriangle } from 'lucide-react';
import ProfessionalCard from './ProfessionalCard';
import './OverviewCards.css';

const OverviewCards = ({ data }) => {
  if (!data) return null;

  const cards = [
    {
      title: '总服务器数',
      value: data.totalServers,
      unit: '台',
      icon: Server,
      isCore: true,
      subtitle: `${data.onlineServers} 台在线`,
      status: 'normal'
    },
    {
      title: 'GPU卡总数',
      value: data.totalGpuCards,
      unit: '张',
      icon: HardDrive,
      isCore: true,
      subtitle: `${data.onlineGpuCards} 张在线`,
      status: 'normal'
    },
    {
      title: '服务器在线率',
      value: data.onlineRate,
      unit: '%',
      icon: Activity,
      subtitle: `${data.onlineServers}/${data.totalServers} 在线`,
      status: parseFloat(data.onlineRate) < 90 ? 'warning' : 'success'
    },
    {
      title: 'GPU在线率',
      value: data.gpuOnlineRate,
      unit: '%',
      icon: Activity,
      subtitle: `${data.onlineGpuCards}/${data.totalGpuCards} 在线`,
      status: parseFloat(data.gpuOnlineRate) < 90 ? 'warning' : 'success'
    },
    {
      title: 'CPU平均使用率',
      value: data.avgCpuUsage,
      unit: '%',
      icon: Cpu,
      status: parseFloat(data.avgCpuUsage) > 80 ? 'critical' : parseFloat(data.avgCpuUsage) > 60 ? 'warning' : 'success'
    },
    {
      title: 'GPU平均使用率',
      value: data.avgGpuUsage,
      unit: '%',
      icon: HardDrive,
      status: parseFloat(data.avgGpuUsage) > 85 ? 'critical' : parseFloat(data.avgGpuUsage) > 70 ? 'warning' : 'success'
    },
    {
      title: '网络流量',
      value: data.networkTraffic.split(' ')[0],
      unit: 'Mbps',
      icon: Wifi,
      status: 'normal'
    },
    {
      title: '活跃告警',
      value: data.activeAlerts,
      unit: '条',
      icon: AlertTriangle,
      status: data.activeAlerts > 10 ? 'critical' : data.activeAlerts > 5 ? 'warning' : 'success'
    }
  ];

  return (
    <div className="overview-cards">
      {cards.map((card, index) => (
        <ProfessionalCard
          key={index}
          icon={card.icon}
          title={card.title}
          value={card.value}
          unit={card.unit}
          subtitle={card.subtitle}
          status={card.status}
          isCore={card.isCore}
        />
      ))}
    </div>
  );
};

export default OverviewCards;
