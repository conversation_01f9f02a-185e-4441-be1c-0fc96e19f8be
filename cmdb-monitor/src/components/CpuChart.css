.cpu-chart {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 16px;
}

.chart-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.chart-title-section h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.chart-controls-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  position: relative;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #666;
  font-size: 16px;
}

.chart-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 8px 16px;
  background: #fafafa;
  border-radius: 8px;
  min-width: 80px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
}

.stat-value.normal {
  color: #52c41a;
}

.stat-value.warning {
  color: #faad14;
}

.stat-value.danger {
  color: #f5222d;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .cpu-chart {
    padding: 16px;
  }
  
  .chart-container {
    min-height: 280px;
  }
  
  .chart-stats {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .stat-item {
    padding: 6px 12px;
    min-width: 70px;
  }
  
  .stat-value {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .cpu-chart {
    padding: 12px;
  }
  
  .chart-container {
    min-height: 250px;
  }
  
  .chart-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-item {
    flex-direction: row;
    justify-content: space-between;
    padding: 8px 12px;
    min-width: auto;
  }
  
  .stat-label {
    margin-bottom: 0;
    margin-right: 8px;
  }
  
  .stat-value {
    font-size: 14px;
  }
}

/* 动画效果 */
.cpu-chart {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-item {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为统计项添加延迟动画 */
.stat-item:nth-child(1) { animation-delay: 0.1s; }
.stat-item:nth-child(2) { animation-delay: 0.2s; }
.stat-item:nth-child(3) { animation-delay: 0.3s; }

/* 状态指示器动画 */
.stat-value.danger {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}
