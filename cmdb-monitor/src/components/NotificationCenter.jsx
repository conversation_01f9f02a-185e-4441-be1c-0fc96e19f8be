import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, BellOff } from 'lucide-react';
import { notificationManager } from '../utils/notification';
import './NotificationCenter.css';

const NotificationCenter = () => {
  const [notifications, setNotifications] = useState([]);
  const [isVisible, setIsVisible] = useState(false);
  const [hasUnread, setHasUnread] = useState(false);

  useEffect(() => {
    const unsubscribe = notificationManager.addListener((newNotifications) => {
      setNotifications(newNotifications);
      setHasUnread(newNotifications.length > 0);
    });

    return unsubscribe;
  }, []);

  const handleRemoveNotification = (id) => {
    notificationManager.removeNotification(id);
  };

  const handleClearAll = () => {
    notificationManager.clearAll();
    setHasUnread(false);
  };

  const handleToggleVisibility = () => {
    setIsVisible(!isVisible);
    if (!isVisible) {
      setHasUnread(false);
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
      default:
        return 'ℹ️';
    }
  };

  const getNotificationClass = (type) => {
    return `notification-item ${type}`;
  };

  return (
    <div className="notification-center">
      {/* 通知按钮 */}
      <button 
        className={`notification-toggle ${hasUnread ? 'has-unread' : ''}`}
        onClick={handleToggleVisibility}
        title="通知中心"
      >
        {hasUnread ? <Bell size={20} /> : <BellOff size={20} />}
        {hasUnread && notifications.length > 0 && (
          <span className="notification-badge">{notifications.length}</span>
        )}
      </button>

      {/* 通知面板 */}
      {isVisible && (
        <div className="notification-panel">
          <div className="notification-header">
            <h3>通知中心</h3>
            <div className="notification-actions">
              {notifications.length > 0 && (
                <button 
                  className="clear-all-btn"
                  onClick={handleClearAll}
                  title="清空所有通知"
                >
                  清空
                </button>
              )}
              <button 
                className="close-btn"
                onClick={() => setIsVisible(false)}
                title="关闭"
              >
                <X size={16} />
              </button>
            </div>
          </div>

          <div className="notification-content">
            {notifications.length === 0 ? (
              <div className="no-notifications">
                <BellOff size={48} />
                <p>暂无通知</p>
              </div>
            ) : (
              <div className="notification-list">
                {notifications.map((notification) => (
                  <div 
                    key={notification.id} 
                    className={getNotificationClass(notification.type)}
                  >
                    <div className="notification-icon">
                      {notification.icon || getNotificationIcon(notification.type)}
                    </div>
                    
                    <div className="notification-body">
                      <div className="notification-title">
                        {notification.title}
                      </div>
                      <div className="notification-message">
                        {notification.message}
                      </div>
                      <div className="notification-time">
                        {notification.timestamp.toLocaleTimeString('zh-CN')}
                      </div>
                    </div>
                    
                    <button 
                      className="notification-close"
                      onClick={() => handleRemoveNotification(notification.id)}
                      title="关闭通知"
                    >
                      <X size={14} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 浮动通知 */}
      <div className="floating-notifications">
        {notifications.slice(0, 3).map((notification) => (
          <div 
            key={`floating-${notification.id}`}
            className={`floating-notification ${notification.type}`}
          >
            <div className="floating-notification-content">
              <span className="floating-notification-icon">
                {notification.icon || getNotificationIcon(notification.type)}
              </span>
              <div className="floating-notification-text">
                <div className="floating-notification-title">
                  {notification.title}
                </div>
                <div className="floating-notification-message">
                  {notification.message}
                </div>
              </div>
            </div>
            <button 
              className="floating-notification-close"
              onClick={() => handleRemoveNotification(notification.id)}
            >
              <X size={12} />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default NotificationCenter;
