import React, { useState, useMemo } from 'react';
import { AlertTriangle, AlertCircle, Info, CheckCircle, Filter } from 'lucide-react';
import './AlertList.css';

const AlertList = ({ data, title = '告警信息' }) => {
  const [filterLevel, setFilterLevel] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  // 过滤数据
  const filteredData = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];
    
    return data.filter(alert => {
      const levelMatch = filterLevel === 'all' || alert.level === filterLevel;
      const statusMatch = filterStatus === 'all' || alert.status === filterStatus;
      return levelMatch && statusMatch;
    });
  }, [data, filterLevel, filterStatus]);

  // 获取告警图标
  const getAlertIcon = (level) => {
    switch (level) {
      case 'critical':
        return <AlertTriangle size={16} />;
      case 'warning':
        return <AlertCircle size={16} />;
      case 'info':
        return <Info size={16} />;
      default:
        return <Info size={16} />;
    }
  };

  // 获取告警级别文本
  const getLevelText = (level) => {
    switch (level) {
      case 'critical':
        return '严重';
      case 'warning':
        return '警告';
      case 'info':
        return '信息';
      default:
        return '未知';
    }
  };

  // 获取状态文本
  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return '活跃';
      case 'resolved':
        return '已解决';
      default:
        return '未知';
    }
  };

  // 统计信息
  const stats = useMemo(() => {
    if (!data || !Array.isArray(data)) return null;
    
    const total = data.length;
    const critical = data.filter(alert => alert.level === 'critical').length;
    const warning = data.filter(alert => alert.level === 'warning').length;
    const info = data.filter(alert => alert.level === 'info').length;
    const active = data.filter(alert => alert.status === 'active').length;
    
    return { total, critical, warning, info, active };
  }, [data]);

  if (!data || !Array.isArray(data)) {
    return (
      <div className="alert-list">
        <div className="loading">加载中...</div>
      </div>
    );
  }

  return (
    <div className="alert-list">
      <div className="alert-header">
        <h3 className="alert-title">{title}</h3>
        
        {stats && (
          <div className="alert-stats">
            <span className="stat-item critical">严重: {stats.critical}</span>
            <span className="stat-item warning">警告: {stats.warning}</span>
            <span className="stat-item info">信息: {stats.info}</span>
            <span className="stat-item active">活跃: {stats.active}</span>
          </div>
        )}
      </div>

      <div className="alert-filters">
        <div className="filter-group">
          <Filter size={16} />
          <select 
            value={filterLevel} 
            onChange={(e) => setFilterLevel(e.target.value)}
            className="filter-select"
          >
            <option value="all">所有级别</option>
            <option value="critical">严重</option>
            <option value="warning">警告</option>
            <option value="info">信息</option>
          </select>
          
          <select 
            value={filterStatus} 
            onChange={(e) => setFilterStatus(e.target.value)}
            className="filter-select"
          >
            <option value="all">所有状态</option>
            <option value="active">活跃</option>
            <option value="resolved">已解决</option>
          </select>
        </div>
      </div>

      <div className="alert-content">
        {filteredData.length === 0 ? (
          <div className="no-alerts">
            <CheckCircle size={48} />
            <p>暂无告警信息</p>
          </div>
        ) : (
          <div className="alert-items">
            {filteredData.map((alert, index) => (
              <div 
                key={alert.id || index} 
                className={`alert-item ${alert.level} ${alert.status}`}
              >
                <div className="alert-icon">
                  {getAlertIcon(alert.level)}
                </div>
                
                <div className="alert-info">
                  <div className="alert-main">
                    <span className="alert-type">{alert.type}</span>
                    <span className={`alert-level ${alert.level}`}>
                      {getLevelText(alert.level)}
                    </span>
                  </div>
                  
                  <div className="alert-message">{alert.message}</div>
                  
                  <div className="alert-details">
                    <span className="alert-server">{alert.server}</span>
                    <span className="alert-time">{alert.time}</span>
                    <span className={`alert-status ${alert.status}`}>
                      {getStatusText(alert.status)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AlertList;
