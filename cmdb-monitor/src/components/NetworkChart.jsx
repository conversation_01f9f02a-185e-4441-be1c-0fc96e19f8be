import React, { useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import './MemoryChart.css'; // 复用内存图表的样式

const NetworkChart = ({ data, title = '网络流量监控' }) => {
  const chartOption = useMemo(() => {
    if (!data || !Array.isArray(data)) return {};

    const times = data.map(item => item.time);
    const inboundData = data.map(item => parseFloat(item.inbound));
    const outboundData = data.map(item => parseFloat(item.outbound));

    return {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#333'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: function(params) {
          let result = `<div style="padding: 8px;"><strong>${params[0].axisValue}</strong><br/>`;
          params.forEach(param => {
            result += `<div style="margin: 4px 0;">
              <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
              ${param.seriesName}: ${param.value} Mbps
            </div>`;
          });
          result += '</div>';
          return result;
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        }
      },
      legend: {
        data: ['入站流量', '出站流量'],
        top: 50,
        textStyle: {
          color: '#666'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '25%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: times,
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
          formatter: '{value} Mbps'
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '入站流量',
          type: 'line',
          data: inboundData,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 3
          },
          itemStyle: {
            color: '#1890ff'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.4)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ]
            }
          },
          symbol: 'circle',
          symbolSize: 6,
          emphasis: {
            scale: 1.2
          }
        },
        {
          name: '出站流量',
          type: 'line',
          data: outboundData,
          smooth: true,
          lineStyle: {
            color: '#52c41a',
            width: 3
          },
          itemStyle: {
            color: '#52c41a'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(82, 196, 26, 0.4)' },
                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }
              ]
            }
          },
          symbol: 'circle',
          symbolSize: 6,
          emphasis: {
            scale: 1.2
          }
        }
      ]
    };
  }, [data, title]);

  // 计算网络统计信息
  const networkStats = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return null;
    
    const latest = data[data.length - 1];
    const currentInbound = parseFloat(latest.inbound);
    const currentOutbound = parseFloat(latest.outbound);
    const totalTraffic = currentInbound + currentOutbound;
    
    // 计算平均值
    const avgInbound = data.reduce((sum, item) => sum + parseFloat(item.inbound), 0) / data.length;
    const avgOutbound = data.reduce((sum, item) => sum + parseFloat(item.outbound), 0) / data.length;
    
    return {
      currentInbound: currentInbound.toFixed(0),
      currentOutbound: currentOutbound.toFixed(0),
      totalTraffic: totalTraffic.toFixed(0),
      avgInbound: avgInbound.toFixed(0),
      avgOutbound: avgOutbound.toFixed(0),
      status: totalTraffic > 1000 ? 'warning' : 'normal'
    };
  }, [data]);

  if (!data || !Array.isArray(data)) {
    return (
      <div className="memory-chart">
        <div className="loading">加载中...</div>
      </div>
    );
  }

  return (
    <div className="memory-chart">
      <div className="chart-container">
        <ReactECharts 
          option={chartOption} 
          style={{ height: '300px', width: '100%' }}
          opts={{ renderer: 'canvas' }}
        />
      </div>
      
      {networkStats && (
        <div className="memory-stats">
          <div className="stat-row">
            <span className="stat-label">当前入站</span>
            <span className="stat-value">{networkStats.currentInbound} Mbps</span>
          </div>
          <div className="stat-row">
            <span className="stat-label">当前出站</span>
            <span className="stat-value">{networkStats.currentOutbound} Mbps</span>
          </div>
          <div className="stat-row">
            <span className="stat-label">总流量</span>
            <span className={`stat-value ${networkStats.status}`}>
              {networkStats.totalTraffic} Mbps
            </span>
          </div>
          <div className="stat-row">
            <span className="stat-label">平均入站</span>
            <span className="stat-value">{networkStats.avgInbound} Mbps</span>
          </div>
          <div className="stat-row">
            <span className="stat-label">平均出站</span>
            <span className="stat-value">{networkStats.avgOutbound} Mbps</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default NetworkChart;
