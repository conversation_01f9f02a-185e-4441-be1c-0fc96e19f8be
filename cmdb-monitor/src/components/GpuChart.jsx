import React, { useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import ProfessionalChartContainer from './ProfessionalChartContainer';
import './CpuChart.css'; // 复用CPU图表的样式

const GpuChart = ({ data, title = 'GPU使用率趋势' }) => {
  const chartOption = useMemo(() => {
    if (!data || !Array.isArray(data)) return {};

    const times = data.map(item => item.time);
    const gpu1Data = data.map(item => parseFloat(item.gpu1));
    const gpu2Data = data.map(item => parseFloat(item.gpu2));
    const gpu3Data = data.map(item => parseFloat(item.gpu3));
    const gpu4Data = data.map(item => parseFloat(item.gpu4));
    const averageData = data.map(item => parseFloat(item.average));

    return {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#333'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: function(params) {
          let result = `<div style="padding: 8px;"><strong>${params[0].axisValue}</strong><br/>`;
          params.forEach(param => {
            result += `<div style="margin: 4px 0;">
              <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
              ${param.seriesName}: ${param.value}%
            </div>`;
          });
          result += '</div>';
          return result;
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        }
      },
      legend: {
        data: ['GPU1', 'GPU2', 'GPU3', 'GPU4', '平均值'],
        top: 50,
        textStyle: {
          color: '#666'
        }
      },
      grid: {
        left: '8%',
        right: '8%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: times,
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
          formatter: '{value}%'
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: 'GPU1',
          type: 'line',
          data: gpu1Data,
          smooth: true,
          lineStyle: {
            color: '#722ed1',
            width: 2
          },
          itemStyle: {
            color: '#722ed1'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(114, 46, 209, 0.3)' },
                { offset: 1, color: 'rgba(114, 46, 209, 0.05)' }
              ]
            }
          }
        },
        {
          name: 'GPU2',
          type: 'line',
          data: gpu2Data,
          smooth: true,
          lineStyle: {
            color: '#13c2c2',
            width: 2
          },
          itemStyle: {
            color: '#13c2c2'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(19, 194, 194, 0.3)' },
                { offset: 1, color: 'rgba(19, 194, 194, 0.05)' }
              ]
            }
          }
        },
        {
          name: 'GPU3',
          type: 'line',
          data: gpu3Data,
          smooth: true,
          lineStyle: {
            color: '#eb2f96',
            width: 2
          },
          itemStyle: {
            color: '#eb2f96'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(235, 47, 150, 0.3)' },
                { offset: 1, color: 'rgba(235, 47, 150, 0.05)' }
              ]
            }
          }
        },
        {
          name: 'GPU4',
          type: 'line',
          data: gpu4Data,
          smooth: true,
          lineStyle: {
            color: '#faad14',
            width: 2
          },
          itemStyle: {
            color: '#faad14'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(250, 173, 20, 0.3)' },
                { offset: 1, color: 'rgba(250, 173, 20, 0.05)' }
              ]
            }
          }
        },
        {
          name: '平均值',
          type: 'line',
          data: averageData,
          smooth: true,
          lineStyle: {
            color: '#f5222d',
            width: 3,
            type: 'dashed'
          },
          itemStyle: {
            color: '#f5222d',
            borderWidth: 2
          },
          symbol: 'circle',
          symbolSize: 6
        }
      ]
    };
  }, [data, title]);

  // 计算统计信息
  const stats = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return null;
    
    const latest = data[data.length - 1];
    const avgGpu = parseFloat(latest.average);
    const maxGpu = Math.max(
      parseFloat(latest.gpu1),
      parseFloat(latest.gpu2),
      parseFloat(latest.gpu3),
      parseFloat(latest.gpu4)
    );
    const minGpu = Math.min(
      parseFloat(latest.gpu1),
      parseFloat(latest.gpu2),
      parseFloat(latest.gpu3),
      parseFloat(latest.gpu4)
    );
    
    return {
      current: avgGpu.toFixed(1),
      max: maxGpu.toFixed(1),
      min: minGpu.toFixed(1),
      status: avgGpu > 85 ? 'danger' : avgGpu > 70 ? 'warning' : 'normal'
    };
  }, [data]);

  if (!data || !Array.isArray(data)) {
    return (
      <ProfessionalChartContainer
        title={title}
        subtitle="实时监控GPU使用率变化趋势"
        status="normal"
      >
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '300px' }}>
          <div>加载中...</div>
        </div>
      </ProfessionalChartContainer>
    );
  }

  // 准备指标数据
  const metrics = stats ? [
    {
      id: 'current',
      label: '当前平均',
      value: `${stats.current}%`,
      trend: 'neutral'
    },
    {
      id: 'max',
      label: '最高值',
      value: `${stats.max}%`,
      trend: 'up'
    },
    {
      id: 'min',
      label: '最低值',
      value: `${stats.min}%`,
      trend: 'down'
    }
  ] : [];

  // 确定状态
  const chartStatus = stats ?
    (stats.status === 'danger' ? 'error' :
     stats.status === 'warning' ? 'warning' : 'normal') : 'normal';

  return (
    <ProfessionalChartContainer
      title={title}
      subtitle="实时监控GPU使用率变化趋势"
      metrics={metrics}
      status={chartStatus}
      lastUpdated={new Date()}
    >
      <ReactECharts
        option={chartOption}
        style={{ height: '300px', width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
    </ProfessionalChartContainer>
  );
};

export default GpuChart;
