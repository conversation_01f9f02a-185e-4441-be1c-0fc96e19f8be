.server-status {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.server-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.header-left {
  flex: 1;
}

.server-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0 0 12px 0;
}

.server-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.stat-item.total {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.stat-item.online {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.stat-item.offline {
  background: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}

.stat-item.maintenance {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: #40a9ff;
  transform: translateY(-1px);
}

.server-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  gap: 16px;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  max-width: 300px;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-select {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.server-table-container {
  flex: 1;
  overflow: auto;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.server-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.server-table th {
  background: #fafafa;
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 1;
}

.server-table td {
  padding: 12px 8px;
  border-bottom: 1px solid #f5f5f5;
  color: #666;
}

.server-row:hover {
  background: #f9f9f9;
}

.server-row.offline {
  background: rgba(245, 34, 45, 0.02);
}

.server-row.maintenance {
  background: rgba(250, 173, 20, 0.02);
}

.server-id {
  font-family: monospace;
  font-size: 12px;
  color: #1890ff;
}

.server-name {
  font-weight: 500;
  color: #333;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.online {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.status-badge.offline {
  background: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}

.status-badge.maintenance {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.status-badge.unknown {
  background: rgba(140, 140, 140, 0.1);
  color: #8c8c8c;
}

.no-servers {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.no-servers svg {
  margin-bottom: 12px;
  color: #d9d9d9;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .server-status {
    padding: 16px;
  }

  .server-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-box {
    max-width: none;
  }

  .server-table {
    font-size: 13px;
  }

  .server-table th,
  .server-table td {
    padding: 10px 6px;
  }
}

@media (max-width: 768px) {
  .server-status {
    padding: 12px;
  }

  .server-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .server-stats {
    gap: 8px;
  }

  .stat-item {
    font-size: 11px;
    padding: 3px 6px;
  }

  .refresh-btn {
    align-self: flex-end;
  }

  .server-table-container {
    overflow-x: auto;
  }

  .server-table {
    min-width: 800px;
    font-size: 12px;
  }

  .server-table th,
  .server-table td {
    padding: 8px 4px;
  }
}

/* 动画效果 */
.server-status {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.server-row {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为表格行添加延迟动画 */
.server-row:nth-child(1) { animation-delay: 0.1s; }
.server-row:nth-child(2) { animation-delay: 0.15s; }
.server-row:nth-child(3) { animation-delay: 0.2s; }
.server-row:nth-child(4) { animation-delay: 0.25s; }
.server-row:nth-child(5) { animation-delay: 0.3s; }

/* 状态指示器动画 */
.status-badge.offline {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* 刷新按钮旋转动画 */
.refresh-btn:active svg {
  animation: spin 0.5s ease-in-out;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
