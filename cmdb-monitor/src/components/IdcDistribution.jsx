import React, { useMemo, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { ChevronUp, Server, HardDrive } from 'lucide-react';
import './IdcDistribution.css';

const IdcDistribution = ({ data }) => {
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const chartOption = useMemo(() => {
    if (!data || !Array.isArray(data)) return {};

    // 准备地图数据
    const mapData = data.map(item => ({
      name: item.location,
      value: [item.coordinates[0], item.coordinates[1], item.serverCount],
      itemStyle: {
        color: item.status === 'normal' ? '#52c41a' : '#faad14'
      },
      label: {
        show: true,
        formatter: `{b}\n{c}台`,
        fontSize: 12,
        color: '#333'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 14,
          fontWeight: 'bold'
        }
      }
    }));

    return {
      title: {
        text: 'IDC机房分布',
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 20,
          fontWeight: 'bold',
          color: '#333'
        },
        subtext: '点击机房查看详细信息',
        subtextStyle: {
          fontSize: 12,
          color: '#666'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          const item = data.find(d => d.location === params.name);
          if (!item) return '';
          
          return `
            <div style="padding: 12px; min-width: 200px;">
              <div style="font-weight: bold; margin-bottom: 12px; font-size: 14px; color: #333;">${item.name}</div>
              <div style="margin-bottom: 6px;"><strong>服务器:</strong> ${item.serverCount}台 (${item.onlineCount}台在线)</div>
              <div style="margin-bottom: 6px;"><strong>GPU卡:</strong> ${item.totalGpuCards}张 (${item.onlineGpuCards}张在线)</div>
              <div style="margin-bottom: 6px;"><strong>每台GPU:</strong> ${item.gpuCardsPerServer}张</div>
              <div style="margin-bottom: 6px;"><strong>CPU使用率:</strong> ${item.cpuUsage}%</div>
              <div style="margin-bottom: 6px;"><strong>GPU使用率:</strong> ${item.gpuUsage}%</div>
              <div style="color: ${item.status === 'normal' ? '#52c41a' : '#faad14'};">
                <strong>状态:</strong> ${item.status === 'normal' ? '正常' : '告警'}
              </div>
              <div style="margin-top: 8px; font-size: 12px; color: #999;">点击查看详细信息</div>
            </div>
          `;
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        }
      },
      grid: {
        left: '10%',
        right: '10%',
        top: '20%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        min: 110,
        max: 125,
        show: false
      },
      yAxis: {
        type: 'value',
        min: 20,
        max: 45,
        show: false
      },
      series: [
        {
          type: 'scatter',
          data: mapData,
          symbolSize: function(val) {
            return Math.max(val[2] / 8, 20);
          },
          symbol: 'circle',
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 3,
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          },
          emphasis: {
            scale: 1.3,
            itemStyle: {
              shadowBlur: 25,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              return `${params.name}\n${params.value[2]}台`;
            },
            fontSize: 12,
            color: '#333',
            fontWeight: 'bold'
          }
        },
        {
          type: 'effectScatter',
          data: mapData.filter(item => {
            const originalItem = data.find(d => d.location === item.name);
            return originalItem && originalItem.status === 'warning';
          }),
          symbolSize: function(val) {
            return Math.max(val[2] / 6, 25);
          },
          showEffectOn: 'render',
          rippleEffect: {
            brushType: 'stroke',
            scale: 3,
            period: 2
          },
          itemStyle: {
            color: '#faad14',
            shadowBlur: 15,
            shadowColor: '#faad14'
          }
        }
      ]
    };
  }, [data]);

  // 统计信息
  const stats = useMemo(() => {
    if (!data || !Array.isArray(data)) return null;

    const totalServers = data.reduce((sum, item) => sum + item.serverCount, 0);
    const totalOnline = data.reduce((sum, item) => sum + item.onlineCount, 0);
    const totalGpuCards = data.reduce((sum, item) => sum + (item.totalGpuCards || 0), 0);
    const totalGpuOnline = data.reduce((sum, item) => sum + (item.onlineGpuCards || 0), 0);
    const avgCpuUsage = (data.reduce((sum, item) => sum + parseFloat(item.cpuUsage), 0) / data.length).toFixed(1);
    const avgGpuUsage = (data.reduce((sum, item) => sum + parseFloat(item.gpuUsage), 0) / data.length).toFixed(1);
    const normalRooms = data.filter(item => item.status === 'normal').length;

    return {
      totalServers,
      totalOnline,
      totalGpuCards,
      totalGpuOnline,
      onlineRate: ((totalOnline / totalServers) * 100).toFixed(1),
      gpuOnlineRate: totalGpuCards > 0 ? ((totalGpuOnline / totalGpuCards) * 100).toFixed(1) : '0',
      avgCpuUsage,
      avgGpuUsage,
      normalRooms,
      totalRooms: data.length
    };
  }, [data]);

  if (!data || !Array.isArray(data)) {
    return (
      <div className="idc-distribution">
        <div className="loading">加载中...</div>
      </div>
    );
  }

  // 处理图表点击事件
  const handleChartClick = (params) => {
    if (params.componentType === 'series') {
      const roomData = data.find(room => room.location === params.name);
      if (roomData) {
        setSelectedRoom(roomData);
        setShowDetails(true);
      }
    }
  };

  return (
    <div className="idc-distribution">
      <div className="chart-container">
        <ReactECharts
          option={chartOption}
          style={{ height: '400px', width: '100%' }}
          opts={{ renderer: 'canvas' }}
          onEvents={{ click: handleChartClick }}
        />
      </div>

      {stats && (
        <div className="idc-stats">
          <div className="stat-item">
            <span className="stat-label">机房总数</span>
            <span className="stat-value">{stats.totalRooms}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">正常机房</span>
            <span className="stat-value">{stats.normalRooms}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">服务器总数</span>
            <span className="stat-value">{stats.totalServers}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">GPU卡总数</span>
            <span className="stat-value">{stats.totalGpuCards}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">服务器在线率</span>
            <span className="stat-value">{stats.onlineRate}%</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">GPU在线率</span>
            <span className="stat-value">{stats.gpuOnlineRate}%</span>
          </div>
        </div>
      )}

      {/* 详细信息面板 */}
      {showDetails && selectedRoom && (
        <div className="room-details-panel">
          <div className="details-header">
            <h3>{selectedRoom.name} - 详细信息</h3>
            <button
              className="close-details"
              onClick={() => setShowDetails(false)}
            >
              <ChevronUp size={16} />
            </button>
          </div>

          <div className="details-content">
            <div className="details-grid">
              <div className="detail-card">
                <div className="detail-icon">
                  <Server size={24} />
                </div>
                <div className="detail-info">
                  <div className="detail-title">服务器</div>
                  <div className="detail-value">{selectedRoom.serverCount} 台</div>
                  <div className="detail-subtitle">{selectedRoom.onlineCount} 台在线</div>
                </div>
              </div>

              <div className="detail-card">
                <div className="detail-icon">
                  <HardDrive size={24} />
                </div>
                <div className="detail-info">
                  <div className="detail-title">GPU卡</div>
                  <div className="detail-value">{selectedRoom.totalGpuCards} 张</div>
                  <div className="detail-subtitle">{selectedRoom.onlineGpuCards} 张在线</div>
                </div>
              </div>

              <div className="detail-card">
                <div className="detail-info">
                  <div className="detail-title">CPU使用率</div>
                  <div className="detail-value">{selectedRoom.cpuUsage}%</div>
                </div>
              </div>

              <div className="detail-card">
                <div className="detail-info">
                  <div className="detail-title">GPU使用率</div>
                  <div className="detail-value">{selectedRoom.gpuUsage}%</div>
                </div>
              </div>

              <div className="detail-card">
                <div className="detail-info">
                  <div className="detail-title">每台GPU配置</div>
                  <div className="detail-value">{selectedRoom.gpuCardsPerServer} 张/台</div>
                </div>
              </div>

              <div className="detail-card">
                <div className="detail-info">
                  <div className="detail-title">机房状态</div>
                  <div className={`detail-value status-${selectedRoom.status}`}>
                    {selectedRoom.status === 'normal' ? '正常' : '告警'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IdcDistribution;
