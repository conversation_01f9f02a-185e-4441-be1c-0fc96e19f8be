# CMDB资产管理监控大屏

一个现代化的CMDB（配置管理数据库）资产管理监控大屏系统，用于实时监控IDC机房信息以及GPU、CPU等核心指标。

## 🚀 功能特性

### 📊 核心监控功能
- **总览统计**: 实时显示服务器总数、在线率、CPU/GPU平均使用率等关键指标
- **IDC机房分布**: 可视化展示各个IDC机房的地理位置和服务器分布情况
- **CPU监控**: 实时CPU使用率趋势图，支持多服务器对比
- **GPU监控**: GPU使用率监控，支持多GPU卡显示
- **内存监控**: 内存使用分布饼图，显示已使用、缓存、空闲内存
- **网络监控**: 网络流量折线图，显示入站和出站流量
- **告警管理**: 实时告警信息列表，支持级别分类和状态筛选
- **服务器状态**: 详细的服务器状态表格，支持搜索和筛选

### 🎛️ 交互功能
- **实时数据更新**: 自动刷新数据，可配置更新间隔
- **通知中心**: 智能通知系统，实时推送告警和状态变化
- **图表控制**: 支持暂停/恢复自动刷新、手动刷新、全屏显示
- **数据筛选**: 可筛选显示特定服务器或数据类型
- **时间范围选择**: 支持不同时间范围的历史数据查看
- **数据导出**: 支持导出图表为PNG、JPG、SVG或CSV格式

### 🎨 用户体验
- **响应式设计**: 适配不同屏幕尺寸，支持移动端访问
- **现代化UI**: 采用渐变背景、毛玻璃效果、流畅动画
- **性能优化**: 内置性能监控，确保流畅运行

## 🛠️ 技术栈

- **前端框架**: React 18 + Vite
- **图表库**: ECharts + echarts-for-react
- **图标库**: Lucide React
- **样式**: CSS3 + CSS Grid + Flexbox
- **状态管理**: React Hooks
- **构建工具**: Vite

## 📦 安装和运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:5173 查看监控大屏

### 构建生产版本
```bash
npm run build:optimize
```

## 🔧 主要组件

- **Dashboard**: 主仪表板组件
- **OverviewCards**: 总览统计卡片
- **IdcDistribution**: IDC机房分布图
- **CpuChart/GpuChart**: CPU/GPU监控图表
- **MemoryChart**: 内存使用监控
- **NetworkChart**: 网络流量监控
- **AlertList**: 告警信息列表
- **ServerStatus**: 服务器状态表格
- **NotificationCenter**: 通知中心

## ⚙️ 配置说明

数据更新间隔可在 `src/config/dashboard.js` 中配置：

```javascript
UPDATE_INTERVALS: {
  OVERVIEW: 30000,      // 总览数据 - 30秒
  CHARTS: 10000,        // 图表数据 - 10秒
  ALERTS: 5000,         // 告警数据 - 5秒
  SERVERS: 15000,       // 服务器状态 - 15秒
}
```

## 📊 性能监控

系统内置性能监控功能，在开发环境中自动启用。在浏览器控制台中输入 `performanceMonitor.getPerformanceReport()` 查看性能报告。

---

**CMDB资产管理监控大屏** - 让监控变得更加直观和高效！ 🎯
