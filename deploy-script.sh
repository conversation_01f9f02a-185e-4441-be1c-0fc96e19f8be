#!/bin/bash

# 部署脚本 - 在服务器上运行
echo "开始部署 CMDB Monitor 项目..."

# 创建部署目录
sudo mkdir -p /opt/cmdb-monitor
cd /opt/cmdb-monitor

# 解压项目
echo "解压项目文件..."
sudo tar -xzf /tmp/cmdb-monitor-deploy.tar.gz
sudo chown -R root:root cmdb-monitor/

# 进入项目目录
cd cmdb-monitor

# 检查并安装Node.js和npm
if ! command -v node &> /dev/null; then
    echo "安装Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# 安装全局依赖
echo "安装全局依赖..."
npm install -g serve

# 使用serve启动静态文件服务
echo "启动项目..."
cd dist
nohup serve -s . -l 3000 > /var/log/cmdb-monitor.log 2>&1 &

echo "部署完成！项目运行在 http://59.110.81.13:3000"
echo "日志文件: /var/log/cmdb-monitor.log"
